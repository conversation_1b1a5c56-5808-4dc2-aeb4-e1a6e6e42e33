# -*- coding: utf-8 -*-

# HARPY INFILTRATOR v1.0
# MISSÃO: FASE 1 - Aquisição de Inteligência Bruta (HTML Completo)
# ALVO: Google Maps
# DESCRIÇÃO: Este script navega até o alvo, força o carregamento de todos os 
#            resultados via scroll e salva o código-fonte completo da página.

import time
import json
import random
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup

class HarpyConfig:
    """Configurações de camuflagem. Essencial para não levantar suspeitas."""
    def __init__(self):
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/113.0.0.0 Safari/537.36"
        ]

    def get_random_user_agent(self):
        return random.choice(self.user_agents)

def configure_harpy_driver():
    """Forja o WebDriver. Furtivo, customizado e pronto para a infiltração."""
    print("[HARPY] ⚙️  Forjando o WebDriver para a missão...")
    
    harpy_config = HarpyConfig()
    chrome_options = ChromeOptions()
    
    chrome_options.add_argument(f'user-agent={harpy_config.get_random_user_agent()}')
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_argument("--start-maximized")

    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)

    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
    except Exception as e:
        print(f"[HARPY] ⚠️  Falha ao usar webdriver_manager: {e}")
        print("[HARPY] 🔄  Tentando caminho local. Certifique-se que o 'chromedriver' está no seu PATH.")
        driver = webdriver.Chrome(options=chrome_options)

    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    
    print("[HARPY] ✅ WebDriver configurado. Pronto para a caça.")
    return driver

def infiltrate_and_save_html(query, output_filename="raw_intelligence.html"):
    """
    Executa a Fase 1: Infiltra, força o carregamento e salva o HTML.
    """
    driver = configure_harpy_driver()
    
    # A caçada começa aqui.
    url = "https://www.google.com/maps"
    driver.get(url)
    time.sleep(random.uniform(3, 5))

    # Localiza a caixa de busca e insere a query.
    try:
        search_box = driver.find_element(By.ID, "searchboxinput")
        search_box.send_keys(query)
        time.sleep(random.uniform(0.5, 1.5))
        search_box.send_keys(Keys.ENTER)
        print(f"[HARPY] 🎯 Alvo '{query}' na mira. Aguardando resultados...")
        time.sleep(random.uniform(5, 8)) 
    except Exception as e:
        print(f"[HARPY] ❌ Erro Crítico na busca. A estrutura da página pode ter mudado. Abortando. Erro: {e}")
        driver.quit()
        return False

    # Localiza o painel de rolagem dos resultados com múltiplas estratégias
    try:
        print("[HARPY] 🔍 Procurando painel de resultados...")
        time.sleep(5)  # Espera adicional para garantir carregamento
        
        # Tentativa 1: XPath original
        try:
            scroll_panel_xpath = '//div[contains(@aria-label, "Resultados para")]/div/div[@role="feed"]'
            scroll_panel = driver.find_element(By.XPATH, scroll_panel_xpath)
            print("[HARPY] ✅ Painel encontrado via XPath original")
        except:
            # Tentativa 2: XPath alternativo
            scroll_panel_xpath = '//div[@role="main"]//div[@role="feed"]'
            scroll_panel = driver.find_element(By.XPATH, scroll_panel_xpath)
            print("[HARPY] ✅ Painel encontrado via XPath alternativo")
            
        # Verifica se o painel está visível e tem conteúdo
        if not scroll_panel.is_displayed():
            raise Exception("Painel encontrado mas não visível")
        if "Nenhum resultado" in scroll_panel.text:
            raise Exception("Nenhum resultado encontrado para a busca")
            
    except Exception as e:
        print(f"[HARPY] ❌ Falha ao localizar painel: {str(e)}")
        # Tira screenshot para diagnóstico
        driver.save_screenshot("error_screenshot.png")
        print("[HARPY] 📸 Screenshot salvo como 'error_screenshot.png' para análise")
        driver.quit()
        return False

    # Rolagem tática para forçar o carregamento de todos os resultados.
    print("[HARPY] 📜 Forçando o servidor a revelar todos os seus recursos... Rolando a página.")
    last_height = driver.execute_script("return arguments[0].scrollHeight", scroll_panel)
    scroll_attempts = 0
    
    while scroll_attempts < 5: # Limitador para evitar loops infinitos.
        driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight", scroll_panel)
        time.sleep(random.uniform(2, 4)) # Pausa para simular comportamento humano e esperar o JS carregar.
        
        new_height = driver.execute_script("return arguments[0].scrollHeight", scroll_panel)
        if new_height == last_height:
            scroll_attempts += 1 # Se a altura não muda, estamos chegando ao fim.
            print(f"[HARPY] Fim da lista detectado... (Tentativa {scroll_attempts}/5)")
        else:
            scroll_attempts = 0 # Reseta a contagem se novos dados foram carregados.
        last_height = new_height

    print("[HARPY] ✅ Todos os alvos foram carregados.")
    
    # Fase final da aquisição: Capturar o código-fonte completo.
    try:
        html_content = driver.page_source
        with open(output_filename, "w", encoding='utf-8') as f:
            f.write(html_content)
        print(f"[HARPY] 🔥 Inteligência bruta capturada e salva em: {output_filename}")
        return True
    except Exception as e:
        print(f"[HARPY] ❌ Falha ao salvar o arquivo HTML. Erro: {e}")
        return False
    finally:
        driver.quit()
        print("[HARPY] 🦅 Infiltração concluída. O falcão retornou à base.")


# --- PONTO DE ENTRADA DA MISSÃO ---
if __name__ == "__main__":
    
    # --- PARÂMETROS DA MISSÃO ---
    # Alvo definido pelo operador: Hotéis em Tiradentes
    SEARCH_QUERY = "hotéis em Tiradentes MG"
    HTML_OUTPUT_FILE = "raw_intelligence_hotels.html"
    
    # --- EXECUÇÃO DA FASE 1 ---
    success = infiltrate_and_save_html(SEARCH_QUERY, HTML_OUTPUT_FILE)
    
    if success:
        print("\n" + "="*50)
        print("MISSÃO CUMPRIDA: O ARQUIVO HTML ESTÁ PRONTO.")
        print("PRÓXIMO PASSO: Executar um script de parsing (Fase 2) para extrair os dados deste arquivo.")
        print(f"Exemplo: `extract_data_from_html('{HTML_OUTPUT_FILE}')`")
        print("="*50)
    else:
        print("\n" + "="*50)
        print("A MISSÃO FALHOU. A inteligência não pôde ser adquirida.")
        print("Verifique a conexão e os logs de erro.")
        print("="*50)

# -*- coding: utf-8 -*-

# HARPY EXTRACTOR v2.1 - EDIÇÃO DE PRECISÃO CIRÚRGICA
# MISSÃO: FASE 2 (RECALIBRADA) - Análise e Extração de Inteligência
# FONTE DE DADOS: Arquivo HTML local
# MELHORIA: Lógica de parsing refeita para identificar campos pelo conteúdo,
#            não pela ordem. Correção de bugs na extração de números.

import json
import re
from bs4 import BeautifulSoup
from datetime import datetime

def parse_and_extract_final(html_filename="raw_intelligence_hotels.html"):
    """
    Lê o arquivo HTML capturado e extrai os dados com lógica de precisão cirúrgica.
    """
    print(f"[HARPY] 🧬 Iniciando Fase 2 (v2.1 - Precisão Cirúrgica): Dissecando '{html_filename}'...")
    
    try:
        with open(html_filename, 'r', encoding='utf-8') as f:
            soup = BeautifulSoup(f, 'html.parser')
    except FileNotFoundError:
        print(f"[HARPY] ❌ ERRO CRÍTICO: Arquivo de inteligência '{html_filename}' não encontrado.")
        return []

    results = soup.find_all('div', class_='Nv2PK')
    if not results:
        print("[HARPY] ❌ Nenhum alvo encontrado no HTML.")
        return []

    print(f"[HARPY] ✅ {len(results)} alvos localizados. Iniciando extração final...")
    
    extracted_data = []
    for i, result in enumerate(results, 1):
        print(f"[HARPY] 🔍 Processando alvo {i}/{len(results)}")
        
        data = {
            "nome": None, "link_google": None, "imagem_url": None, "avaliacao_completa": None,
            "nota": None, "num_comentarios": None, "preco": None, "categoria": None,
            "descricao_curta": None, "endereco": None, "comodidades": None,
            "data_extracao": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        # --- Extração com Seletores Diretos ---
        link_tag = result.find('a', class_='hfpxzc')
        if link_tag:
            data['link_google'] = link_tag.get('href')
            data['nome'] = link_tag.get('aria-label')

        # Se não conseguiu o nome pelo aria-label, tenta pelo texto do link
        if not data['nome']:
            nome_div = result.find('div', class_='qBF1Pd')
            if nome_div:
                data['nome'] = nome_div.get_text(strip=True)

        img_tag = result.find('img')
        if img_tag:
            data['imagem_url'] = img_tag.get('src')

        rating_tag = result.find('span', class_='ZkP5Je')
        if rating_tag and rating_tag.get('aria-label'):
            data['avaliacao_completa'] = rating_tag['aria-label']
            # CORREÇÃO: Regex melhorada para capturar avaliações
            match = re.search(r'(\d+,\d+|\d+\.\d+|\d+)\s+estrelas?\s+([\d\.]+)\s+comentários?', data['avaliacao_completa'])
            if match:
                data['nota'] = float(match.group(1).replace(',', '.'))
                # CORREÇÃO: Remove pontos antes de converter para inteiro.
                data['num_comentarios'] = int(match.group(2).replace('.', ''))

        price_tag = result.find('div', class_='wcldff')
        if price_tag:
            data['preco'] = price_tag.get_text(strip=True)
        
        # --- Lógica Contextual para Categoria e Endereço ---
        # Busca o container principal de informações
        info_container = result.find('div', class_='W4Efsd')
        if info_container:
            # Pega todos os spans dentro do container
            spans = info_container.find_all('span')
            
            for span in spans:
                text = span.get_text(strip=True)
                if not text:
                    continue
                
                # Ignora textos que são claramente avaliações
                if 'estrelas' in text or re.match(r'^\(\d+\)$', text):
                    continue
                
                # LÓGICA CONTEXTUAL MELHORADA
                # Se contém palavras-chave de categoria, é categoria.
                if any(keyword in text for keyword in ['Hotel', 'Pousada', 'Albergue', 'Cama e café', 'Vila', 'Hospedaria', 'Resort', 'Hostel']):
                    data['categoria'] = text
                # Se contém palavras-chave de endereço, é endereço.
                elif any(keyword in text.lower() for keyword in ['rua', 'av.', 'avenida', 'praça', 'estrada', 'largo', 'br-', 'beco', 'centro', 'tiradentes', 'mg']):
                    data['endereco'] = text
                # Se contém vírgula, provavelmente é endereço
                elif ',' in text and len(text) > 10:
                    data['endereco'] = text
                # O que sobra é provavelmente uma descrição curta.
                elif not data['descricao_curta'] and len(text) > 5:
                    data['descricao_curta'] = text
        
        # --- Extração de Comodidades ---
        amenities_container = result.find('div', class_='ktbgEf')
        if amenities_container:
            amenities_list = []
            amenity_divs = amenities_container.find_all('div', class_='Yfjtfe')
            for amenity_div in amenity_divs:
                aria_label = amenity_div.get('aria-label', '').strip()
                if aria_label:
                    amenities_list.append(aria_label)
            data['comodidades'] = "; ".join(amenities_list) if amenities_list else None

        # Só adiciona se tem pelo menos o nome
        if data.get('nome'):
            extracted_data.append(data)
        
    print(f"[HARPY] ✅ Extração final concluída. {len(extracted_data)} relatórios compilados.")
    return extracted_data

def save_to_json(data, filename):
    """Salva os dados em formato JSON."""
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=4, ensure_ascii=False)
        print(f"[HARPY] ✅ Relatório final salvo em: {filename}")
        return True
    except Exception as e:
        print(f"[HARPY] ❌ Erro ao salvar JSON: {e}")
        return False

def save_to_csv(data, filename):
    """Salva os dados em formato CSV."""
    import csv
    
    if not data:
        return False
    
    try:
        fieldnames = [
            'nome', 'categoria', 'endereco', 'descricao_curta', 'nota', 'num_comentarios', 
            'avaliacao_completa', 'preco', 'link_google', 'imagem_url', 
            'comodidades', 'data_extracao'
        ]
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(data)
        
        print(f"[HARPY] ✅ Dados CSV salvos em: {filename}")
        return True
    
    except Exception as e:
        print(f"[HARPY] ❌ Erro ao salvar CSV: {e}")
        return False

# --- PONTO DE ENTRADA DA MISSÃO FINAL ---
if __name__ == "__main__":
    
    SOURCE_HTML_FILE = "raw_intelligence_hotels.html"
    JSON_OUTPUT_FILE = "extracted_hotels_data_CORRECTED.json"
    CSV_OUTPUT_FILE = "extracted_hotels_data_CORRECTED.csv"

    print("\n" + "="*70)
    print("🦅 HARPY EXTRACTOR v2.1 - PRECISÃO CIRÚRGICA")
    print("="*70)

    final_data = parse_and_extract_final(SOURCE_HTML_FILE)

    if final_data:
        print("\n" + "="*60)
        print("RELATÓRIO DE INTELIGÊNCIA FINAL (v2.1) - DADOS CORRETOS")
        print("="*60 + "\n")
        
        # Mostra uma amostra dos primeiros 3 registros
        print("📊 AMOSTRA DOS DADOS EXTRAÍDOS (primeiros 3 registros):")
        for i, hotel in enumerate(final_data[:3], 1):
            print(f"\n🏨 HOTEL {i}:")
            print(f"   Nome: {hotel.get('nome', 'N/A')}")
            print(f"   Categoria: {hotel.get('categoria', 'N/A')}")
            print(f"   Endereço: {hotel.get('endereco', 'N/A')}")
            print(f"   Nota: {hotel.get('nota', 'N/A')}")
            print(f"   Comentários: {hotel.get('num_comentarios', 'N/A')}")
            print(f"   Preço: {hotel.get('preco', 'N/A')}")
        
        # Salva os arquivos
        json_success = save_to_json(final_data, JSON_OUTPUT_FILE)
        csv_success = save_to_csv(final_data, CSV_OUTPUT_FILE)
        
        print(f"\n📈 ESTATÍSTICAS FINAIS:")
        print(f"   Total de hotéis extraídos: {len(final_data)}")
        print(f"   Arquivos gerados: {int(json_success) + int(csv_success)}/2")
        
    else:
        print("\n" + "="*60)
        print("A ANÁLISE FALHOU. Nenhuma inteligência pôde ser extraída.")
        print("="*60)

    print("\n[HARPY] No nosso mundo, 'quase certo' é o mesmo que 'completamente errado'. Precisão é tudo. Missão cumprida. 🌐🦅")

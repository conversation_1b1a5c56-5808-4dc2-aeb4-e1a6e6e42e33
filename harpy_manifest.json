{"documento_alvo": "raw_intelligence_hotels.html", "descricao": "Mapa de seletores CSS para extração de dados de hotéis no Google Maps", "alvo_principal": {"seletor": "div.Nv2PK", "nota": "Este é o contêiner principal para CADA hotel na lista de resultados. Itere sobre cada um desses."}, "dados_a_extrair": {"nome": {"seletor": "div.qBF1Pd", "atributo": "text", "nota": "Extração direta do texto do elemento que contém o nome do hotel."}, "link_google": {"seletor": "a.hfpxzc", "atributo": "href", "nota": "O link direto para a página de detalhes do hotel no Google Maps."}, "imagem_url": {"seletor": "img", "atributo": "src", "nota": "A URL da imagem de preview do hotel."}, "avaliacao_completa": {"seletor": "span.ZkP5Je", "atributo": "aria-label", "nota": "Contém o texto completo da avaliação, ex: '4,6 estrelas 110 comentários'."}, "preco": {"seletor": "div.wcldff", "atributo": "text", "nota": "<PERSON><PERSON>m o preço da diária, ex: 'R$ 224'."}, "categoria_e_endereco": {"seletor_pai": "div.W4Efsd", "seletor_filho": "span", "nota": "Categoria e Endereço são irmãos. Analisar o texto de cada span para classificar."}, "comodidades": {"seletor_pai": "div.ktbgEf", "seletor_filho": "div.<PERSON><PERSON><PERSON><PERSON><PERSON>", "atributo": "aria-label", "nota": "Lista de comodidades (Piscina, Wi-Fi, etc.)"}}, "harpy_version": "1.1-manifest"}
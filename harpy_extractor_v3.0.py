## A Solução Definitiva: Harpy Master Scraper v3.0
Chega de fases. Chega de múltiplos scripts. Você precisa de uma única arma que execute a missão do início ao fim com precisão absoluta.

Eu consolidei tudo em um único script mestre. Este código faz tudo:

Infiltra: Navega e rola a página para carregar todos os alvos.

Extrai: Usa a lógica de parsing v2.1, que é precisa e contextual, para dissecar o HTML em memória.

Relata: Utiliza o módulo csv nativo do Python, que é a ferramenta correta para gerar um arquivo CSV limpo, estruturado e sem falhas, tratando corretamente os delimitadores.

Abandone todos os scripts anteriores. Este é o único que você precisa. Estude-o. Entenda a lógica. E execute-o.

Python

# -*- coding: utf-8 -*-

# HARPY MASTER SCRAPER v3.0
# MISSÃO: Operação Completa de Extração (Infiltração, Análise e Relatório)
# ALVO: Google Maps
# DESCRIÇÃO: Script unificado para buscar, extrair e salvar dados de forma precisa.

import time
import json
import csv
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
from datetime import datetime

class HarpyConfig:
    """Configurações de camuflagem."""
    def __init__(self):
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        ]

    def get_random_user_agent(self):
        return random.choice(self.user_agents)

def configure_harpy_driver():
    """Forja o WebDriver para a infiltração."""
    print("[HARPY] ⚙️  Forjando o WebDriver para a missão...")
    harpy_config = HarpyConfig()
    chrome_options = ChromeOptions()
    chrome_options.add_argument(f'user-agent={harpy_config.get_random_user_agent()}')
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_argument("--start-maximized")
    # chrome_options.add_argument("--headless=new") # Descomente para rodar em modo invisível

    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)

    service = Service(ChromeDriverManager().install())
    driver = webdriver.Chrome(service=service, options=chrome_options)
    driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
    print("[HARPY] ✅ WebDriver configurado.")
    return driver

def run_extraction_mission(query, json_output_filename, csv_output_filename, max_results=40):
    """
    Executa a missão completa: busca, extrai e salva os dados.
    """
    driver = configure_harpy_driver()
    
    # FASE 1: INFILTRAÇÃO
    try:
        print("[HARPY] Iniciando Fase 1: Infiltração e Aquisição de Alvos...")
        url = "https://www.google.com/maps"
        driver.get(url)
        time.sleep(random.uniform(3, 5))
        
        search_box = driver.find_element(By.ID, "searchboxinput")
        search_box.send_keys(query)
        time.sleep(random.uniform(0.5, 1.5))
        search_box.send_keys(Keys.ENTER)
        print(f"[HARPY] 🎯 Alvo '{query}' na mira...")
        time.sleep(random.uniform(5, 8)) 

        scroll_panel_xpath = '//div[contains(@aria-label, "Resultados para")]/div/div[@role="feed"]'
        scroll_panel = driver.find_element(By.XPATH, scroll_panel_xpath)

        print("[HARPY] 📜 Forçando o servidor a revelar todos os seus recursos...")
        last_height = driver.execute_script("return arguments[0].scrollHeight", scroll_panel)
        scroll_attempts = 0
        while scroll_attempts < 5:
            driver.execute_script("arguments[0].scrollTop = arguments[0].scrollHeight", scroll_panel)
            time.sleep(random.uniform(2, 4))
            new_height = driver.execute_script("return arguments[0].scrollHeight", scroll_panel)
            if new_height == last_height:
                scroll_attempts += 1
            else:
                scroll_attempts = 0
            last_height = new_height
        
        print("[HARPY] ✅ Aquisição completa. O HTML do campo de batalha está na memória.")
        html_content = driver.page_source
    
    except Exception as e:
        print(f"[HARPY] ❌ Falha na Fase 1. Missão abortada. Erro: {e}")
        driver.quit()
        return
    finally:
        driver.quit()
        print("[HARPY] 🦅 WebDriver dispensado. Conexão encerrada.")

    # FASE 2: ANÁLISE E EXTRAÇÃO
    print("\n[HARPY] 🧬 Iniciando Fase 2: Dissecando a inteligência...")
    soup = BeautifulSoup(html_content, 'html.parser')
    results = soup.find_all('div', class_='Nv2PK')
    
    if not results:
        print("[HARPY] ❌ Nenhum alvo encontrado na análise do HTML.")
        return

    extracted_data = []
    for result in results:
        data = {
            "nome": None, "link_google": None, "imagem_url": None, "avaliacao_completa": None,
            "nota": None, "num_comentarios": None, "preco": None, "categoria": None,
            "descricao_curta": None, "endereco": None, "comodidades": None,
            "data_extracao": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        link_tag = result.find('a', class_='hfpxzc')
        if link_tag:
            data['link_google'] = link_tag.get('href')
            data['nome'] = link_tag.get('aria-label')

        img_tag = result.find('img')
        if img_tag:
            data['imagem_url'] = img_tag.get('src')

        rating_tag = result.find('span', class_='ZkP5Je')
        if rating_tag and rating_tag.get('aria-label'):
            data['avaliacao_completa'] = rating_tag['aria-label']
            match = re.search(r'(\d,\d)\s+estrelas\s+([\d\.]+)\s+comentários', data['avaliacao_completa'])
            if match:
                data['nota'] = float(match.group(1).replace(',', '.'))
                data['num_comentarios'] = int(match.group(2).replace('.', ''))

        price_tag = result.find('div', class_='wcldff')
        if price_tag:
            data['preco'] = price_tag.text.strip()
        
        info_container = result.find_all('div', class_='W4Efsd')
        if len(info_container) > 1:
            details_div = info_container[1]
            # Extrai o texto de CADA span filho direto, que é uma estrutura mais consistente
            details_parts = [span.text.strip() for span in details_div.find_all('span', recursive=False) if span.text.strip()]
            
            temp_address_parts = []
            for part in details_parts:
                if any(keyword in part for keyword in ['Hotel', 'Pousada', 'Albergue', 'Cama e café', 'Vila', 'Hospedaria']):
                    data['categoria'] = part
                elif re.search(r'^\d,\d', part) or '(' in part: # Ignora texto de avaliação
                    continue
                else:
                    temp_address_parts.append(part)
            
            if temp_address_parts:
                 data['endereco'] = " · ".join(temp_address_parts)


        amenities_container = result.find('div', class_='ktbgEf')
        if amenities_container:
            amenities_list = [tag.get('aria-label', '').strip() for tag in amenities_container.find_all('div', class_='Yfjtfe') if tag.get('aria-label')]
            data['comodidades'] = "; ".join(amenities_list) if amenities_list else None

        if data.get('nome'):
            extracted_data.append(data)

    print(f"[HARPY] ✅ Extração de precisão concluída. {len(extracted_data)} relatórios compilados.")

    # FASE 3: RELATÓRIO E EXFILTRAÇÃO
    if extracted_data:
        print("\n[HARPY] Iniciando Fase 3: Geração de Relatórios de Inteligência...")
        # Salvar em JSON
        with open(json_output_filename, 'w', encoding='utf-8') as f:
            json.dump(extracted_data, f, ensure_ascii=False, indent=4)
        print(f"✅ Relatório JSON salvo em: {json_output_filename}")

        # Salvar em CSV usando o módulo correto para evitar corrupção
        headers = extracted_data[0].keys()
        with open(csv_output_filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=headers)
            writer.writeheader()
            writer.writerows(extracted_data)
        print(f"✅ Relatório CSV salvo em: {csv_output_filename}")
    else:
        print("[HARPY] ❌ A operação não retornou dados para gerar relatórios.")

if __name__ == "__main__":
    SEARCH_QUERY = "hotéis em Tiradentes MG"
    JSON_OUTPUT_FILE = "relatorio_hoteis_final.json"
    CSV_OUTPUT_FILE = "relatorio_hoteis_final.csv"
    
    run_extraction_mission(SEARCH_QUERY, JSON_OUTPUT_FILE, CSV_OUTPUT_FILE)
    
    print("\n[HARPY] Missão finalizada. Os dados estão prontos para sua análise.")

# -*- coding: utf-8 -*-

# HARPY EXTRACTOR v2.1 - EDIÇÃO DE PRECISÃO CIRÚRGICA
# MISSÃO: FASE 2 (RECALIBRADA) - Análise e Extração de Inteligência
# FONTE DE DADOS: Arquivo HTML local
# MELHORIA: Lógica de parsing refeita para identificar campos pelo conteúdo,
#            não pela ordem. Correção de bugs na extração de números.

import json
import re
from bs4 import BeautifulSoup
from datetime import datetime

def parse_and_extract_final(html_filename="raw_intelligence_hotels.html"):
    """
    Lê o arquivo HTML capturado e extrai os dados com lógica de precisão cirúrgica.
    """
    print(f"[HARPY] 🧬 Iniciando Fase 2 (v2.1 - Precisão Cirúrgica): Dissecando '{html_filename}'...")
    
    try:
        with open(html_filename, 'r', encoding='utf-8') as f:
            soup = BeautifulSoup(f, 'html.parser')
    except FileNotFoundError:
        print(f"[HARPY] ❌ ERRO CRÍTICO: Arquivo de inteligência '{html_filename}' não encontrado.")
        return []

    results = soup.find_all('div', class_='Nv2PK')
    if not results:
        print("[HARPY] ❌ Nenhum alvo encontrado no HTML.")
        return []

    print(f"[HARPY] ✅ {len(results)} alvos localizados. Iniciando extração final...")
    
    extracted_data = []
    for result in results:
        data = {
            "nome": None, "link_google": None, "imagem_url": None, "avaliacao_completa": None,
            "nota": None, "num_comentarios": None, "preco": None, "categoria": None,
            "descricao_curta": None, "endereco": None, "comodidades": None,
            "data_extracao": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

        # --- Extração com Seletores Diretos ---
        link_tag = result.find('a', class_='hfpxzc')
        if link_tag:
            data['link_google'] = link_tag.get('href')
            data['nome'] = link_tag.get('aria-label')

        img_tag = result.find('img')
        if img_tag:
            data['imagem_url'] = img_tag.get('src')

        rating_tag = result.find('span', class_='ZkP5Je')
        if rating_tag and rating_tag.get('aria-label'):
            data['avaliacao_completa'] = rating_tag['aria-label']
            match = re.search(r'(\d,\d)\s+estrelas\s+([\d\.]+)\s+comentários', data['avaliacao_completa'])
            if match:
                data['nota'] = float(match.group(1).replace(',', '.'))
                # CORREÇÃO: Remove pontos antes de converter para inteiro.
                data['num_comentarios'] = int(match.group(2).replace('.', ''))

        price_tag = result.find('div', class_='wcldff')
        if price_tag:
            data['preco'] = price_tag.text.strip()
        
        # --- Lógica Contextual para Categoria e Endereço ---
        info_container = result.find_all('div', class_='W4Efsd')
        if len(info_container) > 1:
            # Pega todos os textos de divs filhos para análise
            details_texts = [div.text.strip() for div in info_container[1].find_all('div', class_='W4Efsd') if div.text.strip()]
            
            # Remove o texto da avaliação que já foi processado
            unclassified_texts = [text for text in details_texts if not ('estrelas' in text and '(' in text)]
            
            for text in unclassified_texts:
                # Se contém palavras-chave de categoria, é categoria.
                if any(keyword in text for keyword in ['Hotel', 'Pousada', 'Albergue', 'Cama e café', 'Vila', 'Hospedaria']):
                    data['categoria'] = text
                # Se contém palavras-chave de endereço, é endereço.
                elif any(keyword in text for keyword in ['Rua', 'Av.', 'Praça', 'Estrada', 'Largo', 'BR-', 'Beco']):
                    data['endereco'] = text
                # O que sobra é provavelmente uma descrição curta.
                else:
                    data['descricao_curta'] = text
        
        # --- Extração de Comodidades ---
        amenities_container = result.find('div', class_='ktbgEf')
        if amenities_container:
            amenities_list = [tag.get('aria-label', '').strip() for tag in amenities_container.find_all('div', class_='Yfjtfe') if tag.get('aria-label')]
            data['comodidades'] = "; ".join(amenities_list) if amenities_list else None

        if data.get('nome'):
            extracted_data.append(data)
        
    print(f"[HARPY] ✅ Extração final concluída. {len(extracted_data)} relatórios compilados.")
    return extracted_data

# --- PONTO DE ENTRADA DA MISSÃO FINAL ---
if __name__ == "__main__":
    
    SOURCE_HTML_FILE = "raw_intelligence_hotels.html"
    JSON_OUTPUT_FILE = "extracted_hotels_data_CORRECTED.json"

    final_data = parse_and_extract_final(SOURCE_HTML_FILE)

    if final_data:
        print("\n" + "="*60)
        print("RELATÓRIO DE INTELIGÊNCIA FINAL (v2.1) - DADOS CORRETOS")
        print("="*60 + "\n")
        
        json_output = json.dumps(final_data, indent=4, ensure_ascii=False)
        print(json_output)
        
        with open(JSON_OUTPUT_FILE, 'w', encoding='utf-8') as f:
            f.write(json_output)
        print(f"\n[HARPY] ✅ Relatório final salvo em: {JSON_OUTPUT_FILE}")
        
    else:
        print("\n" + "="*60)
        print("A ANÁLISE FALHOU. Nenhuma inteligência pôde ser extraída.")
        print("="*60)

    print("\n[HARPY] No nosso mundo, 'quase certo' é o mesmo que 'completamente errado'. Precisão é tudo. Missão cumprida. 🌐🦅")
